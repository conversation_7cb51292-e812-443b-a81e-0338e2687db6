package com.amobear.themepack.presentation.home

import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.amobear.themepack.R
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.presentation.home.components.CoinIndicator
import com.amobear.themepack.presentation.home.navigation.HomeDestination
import com.amobear.themepack.presentation.home.screens.InstallStepsScreen
import com.amobear.themepack.presentation.home.screens.ProfileScreen
import com.amobear.themepack.presentation.home.screens.WallpaperSuccessScreen
import com.amobear.themepack.presentation.home.screens.theme.LocalNavController
import com.amobear.themepack.presentation.home.screens.theme.ThemesScreen
import com.amobear.themepack.presentation.icon_install.IconInstallScreen
import com.amobear.themepack.presentation.icons.iconsGraph
import com.amobear.themepack.presentation.widget.widgetGraph
import com.amobear.themepack.ui.common.shimmerEffect
import kotlinx.coroutines.delay
import timber.log.Timber

// Create a CompositionLocal for NavController
val HomeLocalNavController = staticCompositionLocalOf<NavController?> { null }

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    viewModel: HomeViewModelInterface = hiltViewModel<HomeViewModel>(),
    onNavigateToSettings: () -> Unit = {},
    onNavigateToPurchase: () -> Unit = {},
    onNavigateToRewards: () -> Unit = {},
<<<<<<< HEAD
    onNavigateToThemes: () -> Unit = {},
    onIconCustomizationSelected: () -> Unit = {},
    onWidgetSelected: (String) -> Unit = {},
    onNavigateToAppInstall: (String) -> Unit,
    modifier: Modifier = Modifier
=======
    onIconSelected: (String) -> Unit = {},
    onWidgetSelected: (String) -> Unit = {},
>>>>>>> b3faf06 (Update coin management and UI state handling across the app)
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val navController = rememberNavController()
    val backgroundColor = Color(0xFFF6F2F2)
    val primaryColor = Color(0xFFFF76CE)
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination

    // Add back handler for home screen navigation
    // When on tabs other than THEMES, navigate back to THEMES tab
    // When on THEMES tab, let the system handle back (exit app or go to previous screen)
    BackHandler(
        enabled = currentDestination?.route != HomeDestination.THEMES.route
    ) {
        navController.navigate(HomeDestination.THEMES.route) {
            popUpTo(navController.graph.findStartDestination().id) {
                saveState = true
            }
            launchSingleTop = true
            restoreState = true
        }
    }

    Scaffold(
        topBar = {
            TopBarContent(onNavigateToSettings, onNavigateToRewards, backgroundColor, uiState.coinBalance)
        },
        bottomBar = {
            HomeBottomNavigation(
                findStartDestination = { navController.graph.findStartDestination().id },
                navigate = { route, builder ->
                    navController.navigate(route, builder)
                },
                backgroundColor = backgroundColor,
                primaryColor = primaryColor,
                currentDestination = currentDestination
            )
        },
        containerColor = backgroundColor
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            CompositionLocalProvider(LocalNavController provides navController) {
                HomeNavHost(
                    navController = navController,
                    onIconCustomizationSelected = onIconCustomizationSelected,
                    onWidgetSelected = onWidgetSelected,
                    onNavigateToAppInstall = onNavigateToAppInstall
                )
                SnackUnlockAllFeature(
                    Modifier
                        .fillMaxWidth()
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 12.dp)
                )
            }
        }
    }
}

@Composable
@OptIn(ExperimentalMaterial3Api::class)
private fun TopBarContent(
    onNavigateToSettings: () -> Unit,
    onNavigateToRewards: () -> Unit,
    backgroundColor: Color,
    coinBalance: Int
) {
    TopAppBar(
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Back button placeholder
                Icon(
                    painter = painterResource(id = R.drawable.ic_diamond),
                    contentDescription = "Back",
                    modifier = Modifier.size(24.dp),
                    tint = Color.Unspecified
                )

                // App title
                Text(
                    text = "Themepack",
                    color = Color(0xFFFF76CE),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(start = 4.dp)
                )
            }
        },
        actions = {
            Box(
                modifier = Modifier
                    .padding(end = 8.dp)
            ) {
                CoinIndicator(
                    coins = coinBalance,
                    onAddCoinsClick = onNavigateToRewards,
                    modifier = Modifier.wrapContentSize()
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = backgroundColor
        )
    )
}

@Composable
fun SnackUnlockAllFeature(modifier: Modifier) {
    val context = LocalContext.current
    var isVisible by remember { mutableStateOf(true) }
    
    // Hide the snack after 5 seconds when screen becomes visible
    LaunchedEffect(Unit) {
        delay(5000) // 5 seconds
        isVisible = false
    }
    
    if (isVisible) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 40.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(color = Color(0xffff59c4))
                .shimmerEffect(
                    highlightColor = Color.White.copy(alpha = 0.6f),
                    durationMillis = 3000,
                    angle = 20f
                ),
            contentAlignment = Alignment.Center
        ) {
            Row(
                modifier = Modifier
                    .padding(vertical = 8.dp)
                    .clickable {
                        Toast.makeText(
                            context,
                            "Not implemented yet",
                            Toast.LENGTH_SHORT
                        )
                            .show()
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_crown),
                    contentDescription = "Crown Icon",
                    modifier = Modifier.size(24.dp)
                )
                Text(
                    text = "Unlock all features. No Ads.",
                    color = Color.White,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }
    }
}

@Composable
fun HomeBottomNavigation(
    findStartDestination: () -> Int,
    navigate: (String, NavOptionsBuilder.() -> Unit) -> Unit,
    backgroundColor: Color,
    primaryColor: Color,
    currentDestination: NavDestination?
) {

    BottomAppBar(
        containerColor = backgroundColor,
        tonalElevation = 0.dp
    ) {
        HomeDestination.entries.forEach { screen ->
            val selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true
            val tint = if (selected) primaryColor else Color.Gray

            NavigationBarItem(
                icon = {
                    Icon(
                        painter = painterResource(id = screen.icon),
                        contentDescription = stringResource(id = screen.label),
                        modifier = Modifier.size(24.dp),
                        tint = tint
                    )
                },
                label = {
                    Text(
                        text = stringResource(id = screen.label),
                        fontSize = 12.sp
                    )
                },
                selected = selected,
                onClick = {
                    navigate(screen.route) {
                        popUpTo(findStartDestination()) {
                            saveState = true
                        }
                        launchSingleTop = true
                        restoreState = true
                    }
                },
                colors = NavigationBarItemDefaults.colors(
                    selectedIconColor = primaryColor,
                    selectedTextColor = primaryColor,
                    unselectedIconColor = Color.Gray,
                    unselectedTextColor = Color.Gray,
                    indicatorColor = backgroundColor
                )
            )
        }
    }
}

@Composable
fun HomeNavHost(
    navController: NavHostController,
    onIconCustomizationSelected: () -> Unit,
    onNavigateToAppInstall: (String) -> Unit,
    onWidgetSelected: (String) -> Unit,
) {
    NavHost(
        navController = navController,
        startDestination = HomeDestination.THEMES.route
    ) {
        composable(HomeDestination.THEMES.route) {
            ThemesScreen()
        }

        composable(HomeDestination.PROFILE.route) {
            ProfileScreen()
        }
        iconsGraph(
            onIconCustomizationSelected = onIconCustomizationSelected,
            onNavigateToAppInstall = onNavigateToAppInstall
        )
        widgetGraph(
            onWidgetSelected = onWidgetSelected
        )

        // Install Steps screen
        composable("install_steps_route") {
            val theme = navController.previousBackStackEntry?.savedStateHandle?.get<Theme>("theme")
            InstallStepsScreen(
                theme = theme,
                onBack = { navController.popBackStack() },
                onUnlockWallpaper = {
                    // Navigate back to themes screen and show install dialog
                    navController.previousBackStackEntry?.savedStateHandle?.set(
                        "showInstallDialog",
                        true
                    )
                    navController.previousBackStackEntry?.savedStateHandle?.set(
                        "selectedTheme",
                        theme
                    )
                    navController.popBackStack("theme_detail_route", inclusive = true)
                }
            )
        }

        // Theme Success screen
        composable("theme_success_route") {
            val theme = navController.previousBackStackEntry?.savedStateHandle?.get<Theme>("theme")
            val context = LocalContext.current

            // Extract icons and widgets from the theme data
            val icons = theme?.iconPacks?.flatMap { it.icons }?.take(4) ?: emptyList()
            val widgets = theme?.widgetPacks?.flatMap { it.widgets }?.take(2) ?: emptyList()

            WallpaperSuccessScreen(
                theme = theme,
                icons = icons,
                widgets = widgets,
                onBack = {
                    // Navigate back to home
                    navController.navigate(HomeDestination.THEMES.route) {
                        popUpTo(HomeDestination.THEMES.route) { inclusive = false }
                    }
                },
                onSetupIcons = {
                    // Navigate to icon install screen with theme data
                    navController.currentBackStackEntry?.savedStateHandle?.set("theme", theme)
                    navController.navigate("icon_install_route")
                },
                onSetupWidgets = {
                    // TODO: Navigate to widgets setup screen
                    // For now, just show a message
                    Toast.makeText(context, "Widgets setup not implemented yet", Toast.LENGTH_SHORT)
                        .show()
                }
            )
        }

        // Icon Install screen
        composable("icon_install_route") {
            val theme = navController.previousBackStackEntry?.savedStateHandle?.get<Theme>("theme")
            if (theme == null) {
                Timber.tag("IconInstallScreen")
                    .w("Theme is null, cannot navigate to icon install screen")
                navController.popBackStack()
                return@composable
            }
            IconInstallScreen(
                onBackClick = { navController.popBackStack() },
                theme = theme
            )
        }
    }
}

// ============= HOME SCREEN PREVIEW FUNCTIONS =============

/**
 * Preview of HomeScreen with default state
 */
@Preview(showBackground = true, name = "Home Screen - Top Bar")
@Composable
fun HomeScreenTopBarPreview() {
    TopBarContent(
        onNavigateToSettings = {},
        onNavigateToRewards = {},
        backgroundColor = Color(0xFFF6F2F2),
        coinBalance = 99999
    )
}

@Preview(showBackground = true, name = "Home Screen - Bottom Navigation")
@Composable
fun HomeScreenBottomNavigationPreview() {
    HomeBottomNavigation(
        findStartDestination = { 0 },
        navigate = { _, _ -> },
        backgroundColor = Color(0xFFF6F2F2),
        primaryColor = Color(0xFFFF76CE),
        currentDestination = null
    )
}

@Preview(showBackground = true, name = "Home Screen - Snack Unlock All Feature")
@Composable
fun HomeScreenSnackUnlockAllFeaturePreview() {
    SnackUnlockAllFeature(Modifier)
}