package com.amobear.themepack.presentation.main.navigation


import DownloadProgressScreen
import android.R.attr.theme
import android.app.Activity
import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.amobear.themepack.R
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.presentation.app_install.AppInstallDestination
import com.amobear.themepack.presentation.app_install.appInstallGraph
import com.amobear.themepack.presentation.customize_icon.CustomizeIconDestination
import com.amobear.themepack.presentation.customize_icon.customizeIconGraph
import com.amobear.themepack.presentation.edit_image.editImageGraph
import com.amobear.themepack.presentation.home.HomeScreen
import com.amobear.themepack.presentation.home.screens.InstallStepsScreen
import com.amobear.themepack.presentation.home.screens.WallpaperSuccessScreen
import com.amobear.themepack.presentation.iap.IAPScreen
import com.amobear.themepack.presentation.icon_install.IconInstallScreen
import com.amobear.themepack.presentation.icon_setup_complete.IconSetupCompletionDestination
import com.amobear.themepack.presentation.language.LanguageScreen
import com.amobear.themepack.presentation.icon_setup_complete.iconSetupCompletionGraph
import com.amobear.themepack.presentation.main.ThemeAppViewModel
import com.amobear.themepack.presentation.main.navigation.destination.ChangeLanguageDestination
import com.amobear.themepack.presentation.main.navigation.destination.HomeDestination
import com.amobear.themepack.presentation.main.navigation.destination.IconInstallDestination
import com.amobear.themepack.presentation.main.navigation.destination.ChooseLanguageDestination
import com.amobear.themepack.presentation.main.navigation.destination.IAPDestination
import com.amobear.themepack.presentation.main.navigation.destination.OnboardingDestination
import com.amobear.themepack.presentation.rewards.RewardsDestination
import com.amobear.themepack.presentation.main.navigation.destination.SettingsDestination
import com.amobear.themepack.presentation.main.navigation.destination.SplashDestination
import com.amobear.themepack.presentation.main.navigation.destination.ThemeDownloadDestination
import com.amobear.themepack.presentation.main.navigation.destination.ThemeDownloadingDestination
import com.amobear.themepack.presentation.main.navigation.destination.ThemeNavigationDestination
import com.amobear.themepack.presentation.main.navigation.destination.WallpaperInstallDestination
import com.amobear.themepack.presentation.onboarding.OnboardingScreen
import com.amobear.themepack.presentation.rewards.rewardsGraph
import com.amobear.themepack.presentation.settings.SettingsScreen
import com.amobear.themepack.presentation.splash.SplashScreen
import com.amobear.themepack.presentation.theme_download.ThemeDownloadScreen
import com.amobear.themepack.presentation.wallpaper_generator.wallpaperGeneratorGraph
import com.amobear.themepack.presentation.ai_wallpaper.AiWallpaperDestination
import com.amobear.themepack.presentation.ai_wallpaper.aiWallpaperGeneratorGraph
import com.amobear.themepack.presentation.wallpaper_install.WallpaperInstallScreen
import com.amobear.themepack.ui.common.ExitAppBottomSheet
import timber.log.Timber

val AppLocalNavController = staticCompositionLocalOf<NavHostController?> { null }

@Composable
fun ThemeNavHost(
    navController: NavHostController,
    startDestination: ThemeNavigationDestination,
    onNavigateToDestination: (ThemeNavigationDestination, String) -> Unit,
    onNavigateToDestinationPopUpTo: (ThemeNavigationDestination, String) -> Unit,
    onNavigateToDestinationPopUpToSplash: (ThemeNavigationDestination) -> Unit,
    onBackClick: () -> Unit,
    onShowMessage: (String) -> Unit,
    onSetSystemBarsColorTransparent: () -> Unit,
    onResetSystemBarsColor: () -> Unit,
    showBottomBar: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    mainViewModel: ThemeAppViewModel = hiltViewModel(),
) {
    val activity = LocalContext.current as Activity

    var showLoadingDialog by rememberSaveable { mutableStateOf(false) }

    var showExitBottomSheet by rememberSaveable { mutableStateOf(false) }

    ExitAppBottomSheet(
        isBottomSheetVisible = showExitBottomSheet,
        onExitClicked = {
            showExitBottomSheet = false
            activity.finishAffinity()
        },
        onDismissRequest = {
            showExitBottomSheet = false
        },
    )

    CompositionLocalProvider(AppLocalNavController provides navController) {
        NavHost(
            modifier = modifier,
            navController = navController,
            startDestination = startDestination.route,
        ) {
            // Splash screen
            composable(route = SplashDestination.route) {
                // Hide bottom bar for splash screen
                showBottomBar(false)

                SplashScreen(
                    onSplashFinished = {
                        // Navigate to onboarding after splash screen loading is complete
                        // We use navigateWithPopUpTo to remove the splash screen from the back stack
                        navController.navigate(OnboardingDestination.route) {
                            // Remove splash from back stack so user can't go back to it
                            popUpTo(SplashDestination.route) {
                                inclusive = true
                            }
                        }
                    }
                )
            }

            // Onboarding screen
            composable(route = OnboardingDestination.route) {
                // Hide bottom bar for onboarding screen
                showBottomBar(false)

                OnboardingScreen(
                    onOnboardingFinished = {
                        // Navigate to home screen after onboarding flow is complete
                        // We use a custom navigation with popUpTo to clear the back stack
                        // This ensures the user can't navigate back to the onboarding screen

                        // First, mark onboarding as completed in the ViewModel
                        // (This is already handled in the OnboardingScreen)

                        // Then navigate to the home screen, clearing the back stack

                        /*
                        navController.navigate(HomeDestination.route) {
                            // Remove all previous destinations from back stack
                            // This ensures a clean navigation history
                            popUpTo(OnboardingDestination.route) {
                                inclusive = true
                            }
                            // Avoid multiple copies of the same destination
                            launchSingleTop = true
                        }
                         */
                        navController.navigate(ChooseLanguageDestination.route)
                    }
                )
            }

            // Home screen
            composable(route = HomeDestination.route) {
                // Show bottom bar for home screen
                showBottomBar(true)

                // Navigate to the home screen implementation
                // The HomeScreen composable handles the bottom navigation and nested navigation
                HomeScreen(onNavigateToSettings = {
                    navController.navigate(SettingsDestination.route)
                }, onNavigateToPurchase = {
                    // TODO: Implement purchase navigation
                }, onNavigateToThemes = {
                    // Already in themes by default
                }, onIconCustomizationSelected = {
                    onNavigateToDestination(
                        CustomizeIconDestination, CustomizeIconDestination.route
                    )
                }, onNavigateToRewards = {
                    navController.navigate(RewardsDestination.route)
                }, onWidgetSelected = {
                    // TODO: Implement widget selection
                },
                    onNavigateToAppInstall = { str ->
                        onNavigateToDestination(
                            AppInstallDestination,
                            AppInstallDestination.createNavigationRoute(isFontNeeded = true, iconSectionStr = str)
                        )
                    }
                )
            }

            // Rewards screen
            rewardsGraph(
                onNavigateBack = { navController.popBackStack() }
            )

            // Theme Detail screesn
            composable(ThemeDownloadDestination.route) {
                navController.previousBackStackEntry?.savedStateHandle?.get<Theme>("theme")
                    ?.let { theme ->
                        ThemeDownloadScreen(
                            theme = theme,
                            onBack = {
                                // Use the same navigation logic as BackHandler to avoid conflicts
                                navController.navigate(HomeDestination.route) {
                                    popUpTo(HomeDestination.route) {
                                        inclusive = false
                                    }
                                    launchSingleTop = true
                                }
                            },
                            onNavigateToSuccess = { selectedTheme ->
                                // Navigate to wallpaper install screen after download
                                navController.currentBackStackEntry?.savedStateHandle?.set(
                                    "theme",
                                    selectedTheme
                                )
                                navController.navigate(WallpaperInstallDestination.route) {
                                    launchSingleTop = true
                                }
                            }
                        )
                    }

                // Add back handler to navigate to home
                BackHandler {
                    navController.navigate(HomeDestination.route) {
                        popUpTo(HomeDestination.route) {
                            inclusive = false
                        }
                        launchSingleTop = true
                    }
                }
            }
            // Download Progress screen
            composable(ThemeDownloadingDestination.route) {
                // Add back handler to navigate to home
                BackHandler {
                    navController.navigate(HomeDestination.route) {
                        popUpTo(HomeDestination.route) {
                            inclusive = false
                        }
                        launchSingleTop = true
                    }
                }

                DownloadProgressScreen(
                    onDownloadComplete = {
                        // Navigate to install steps screen
                        navController.navigate(ThemeDownloadingDestination.route) {
                            // Remove download progress screen from back stack
                            popUpTo(ThemeDownloadingDestination.route) { inclusive = true }
                        }
                    }
                )
            }

            customizeIconGraph(
                onNavigateBack = onBackClick,
                onNavigateToAppIconChanging = {
                    onNavigateToDestination(
                        AppInstallDestination, AppInstallDestination.createNavigationRoute(isFontNeeded = false)
                    )
                })

            appInstallGraph(
                onNavigateBack = onBackClick,
                onNavigateToResultScreen = {
                    onNavigateToDestinationPopUpTo(
                        IconSetupCompletionDestination,
                        IconSetupCompletionDestination.route
                    )
                }
            )

            iconSetupCompletionGraph(
                onNavigateBack = onBackClick
            )

            editImageGraph(
                onNavigateBack = onBackClick,
            )
            // Install Steps screen
            composable("install_steps_route") {
                navController.previousBackStackEntry?.savedStateHandle?.get<Theme>("theme")
                    ?.let { theme ->
                        InstallStepsScreen(
                            theme = theme,
                            onBack = {
                                // Use the same navigation logic as BackHandler to avoid conflicts
                                navController.navigate(HomeDestination.route) {
                                    popUpTo(HomeDestination.route) {
                                        inclusive = false
                                    }
                                    launchSingleTop = true
                                }
                            },
                            onUnlockWallpaper = {
                                // Navigate back to themes screen and show install dialog
                                navController.previousBackStackEntry?.savedStateHandle?.set(
                                    "showInstallDialog",
                                    true
                                )
                                navController.previousBackStackEntry?.savedStateHandle?.set(
                                    "selectedTheme",
                                    theme
                                )
                                navController.navigate(HomeDestination.route) {
                                    popUpTo(HomeDestination.route) {
                                        inclusive = false
                                    }
                                    launchSingleTop = true
                                }
                            }
                        )
                    }

                // Add back handler to navigate to home
                BackHandler {
                    navController.navigate(HomeDestination.route) {
                        popUpTo(HomeDestination.route) {
                            inclusive = false
                        }
                        launchSingleTop = true
                    }
                }
            }
            // Wallpaper Install screen
            composable(WallpaperInstallDestination.route) {
                navController.previousBackStackEntry?.savedStateHandle?.get<Theme>("theme")
                    ?.let { theme ->
                        WallpaperInstallScreen(
                            theme = theme,
                            onBack = {
                                // Use the same navigation logic as BackHandler to avoid conflicts
                                navController.navigate(HomeDestination.route) {
                                    popUpTo(HomeDestination.route) {
                                        inclusive = false
                                    }
                                    launchSingleTop = true
                                }
                            },
                            onNavigateToSuccess = { selectedTheme ->
                                // Navigate to success screen
                                navController.currentBackStackEntry?.savedStateHandle?.set(
                                    "theme",
                                    selectedTheme
                                )
                                navController.navigate("theme_success_route") {
                                    launchSingleTop = true
                                }
                            }
                        )
                    }

                // Add back handler to navigate to home
                BackHandler {
                    navController.navigate(HomeDestination.route) {
                        popUpTo(HomeDestination.route) {
                            inclusive = false
                        }
                        launchSingleTop = true
                    }
                }

            }

            // Theme Success screen
            composable("theme_success_route") {
                val theme =
                    navController.previousBackStackEntry?.savedStateHandle?.get<Theme>("theme")
                val context = LocalContext.current

                // Add back handler to navigate to home
                BackHandler {
                    navController.navigate(HomeDestination.route) {
                        popUpTo(HomeDestination.route) {
                            inclusive = false
                        }
                        launchSingleTop = true
                    }
                }

                // Extract icons and widgets from the theme data
                val icons = theme?.iconPacks?.flatMap { it.icons }?.take(4) ?: emptyList()
                val widgets = theme?.widgetPacks?.flatMap { it.widgets }?.take(2) ?: emptyList()

                WallpaperSuccessScreen(
                    theme = theme,
                    icons = icons,
                    widgets = widgets,
                    onBack = {
                        // Navigate back to home
                        navController.navigate(com.amobear.themepack.presentation.home.navigation.HomeDestination.THEMES.route) {
                            popUpTo(com.amobear.themepack.presentation.home.navigation.HomeDestination.THEMES.route) {
                                inclusive = false
                            }
                        }
                    },
                    onSetupIcons = {
                        // Navigate to icon install screen with theme data
                        navController.currentBackStackEntry?.savedStateHandle?.set("theme", theme)
                        navController.navigate(IconInstallDestination.route)
                    },
                    onSetupWidgets = {
                        // TODO: Navigate to widgets setup screen
                        // For now, just show a message
                        Toast.makeText(
                            context,
                            "Widgets setup not implemented yet",
                            Toast.LENGTH_SHORT
                        )
                            .show()
                    }
                )
            }

            wallpaperGeneratorGraph(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToAiWallpaper = {
                    navController.navigate(AiWallpaperDestination.route)
                }
            )

            aiWallpaperGeneratorGraph(
                onNavigateBack = { navController.popBackStack() }
            )

            // Icon Install screen
            composable(IconInstallDestination.route) {
                val theme =
                    navController.previousBackStackEntry?.savedStateHandle?.get<Theme>("theme")
                if (theme == null) {
                    Timber.tag("IconInstallDestination")
                        .w("Theme is null, cannot navigate to icon install screen")
                    // Navigate back to home instead of popBackStack to avoid re-triggering this composable
                    navController.navigate(HomeDestination.route) {
                        popUpTo(HomeDestination.route) {
                            inclusive = false
                        }
                        launchSingleTop = true
                    }
                    return@composable
                }

                // Add back handler to navigate to home
                BackHandler {
                    navController.navigate(HomeDestination.route) {
                        popUpTo(HomeDestination.route) {
                            inclusive = false
                        }
                        launchSingleTop = true
                    }
                }

                IconInstallScreen(
                    onBackClick = {
                        // Use the same navigation logic as BackHandler to avoid conflicts
                        navController.navigate(HomeDestination.route) {
                            popUpTo(HomeDestination.route) {
                                inclusive = false
                            }
                            launchSingleTop = true
                        }
                    },
                    theme = theme
                )
            }

            composable(SettingsDestination.route) {
                SettingsScreen(
                    onBackClick = {
                        navController.popBackStack()
                    },
                    onNavigateToLanguage = {
                        navController.navigate(ChangeLanguageDestination.route)
                    },
                    onClickPremiumBanner = {
                        navController.navigate(IAPDestination.route)
                    }
                )
            }

            composable(ChooseLanguageDestination.route) {
                LanguageScreen(R.string.choose_language, null)
            }

            composable(ChangeLanguageDestination.route) {
                LanguageScreen(R.string.change_language, {
                    navController.popBackStack()
                })
            }

            composable(IAPDestination.route) {
                IAPScreen { navController.popBackStack() }
            }
        }
    }
}